#!/bin/bash

# Script to automate the creation of a custom Linux Mint Kiosk ISO

# Exit immediately if a command exits with a non-zero status.
set -e
# --- Configuration ---
# !! IMPORTANT: Set ORIGINAL_ISO_FILE_PATH to the actual path of your original Linux Mint ISO file.
ORIGINAL_ISO_FILE_PATH="/home/<USER>/CustomMint/linuxmint-22.1-cinnamon-64bit.iso" # Example: /srv/isos/linuxmint-21.3-cinnamon-64bit.iso
KVM_DISK_IMAGE_PATH="/home/<USER>/CustomMint/updated_mint_vms/Linux-Mint-Nexus-clone.qcow2" # Path to your customized KVM disk image (.qcow2)
OUTPUT_ISO_NAME="NexusMint-$(date +%Y%m%d).iso" # Name of the final ISO file, with date
BUILD_ROOT="/home/<USER>/CustomMint/iso_build_temp" # Directory where temporary build files will be stored
FINAL_ISO_PATH="/home/<USER>/CustomMint" # Directory where the final ISO will be saved
#/home/<USER>/CustomMint/updated_mint_vms/Linux-Mint-Nexus-clone.qcow2
# --- Derived Variables ---
ORIGINAL_ISO_MOUNT_PATH="${BUILD_ROOT}/original_iso_content" # Mount point for the original ISO
CUSTOM_ISO_WORKDIR="${BUILD_ROOT}/custom_iso_workdir"
KVM_DISK_MOUNT_POINT="${BUILD_ROOT}/kvm_disk_mount"
OUTPUT_ISO_FULL_PATH="${FINAL_ISO_PATH}/${OUTPUT_ISO_NAME}"
ISOHDPFX_PATH="/usr/share/syslinux/isohdpfx.bin" # Path to isohybrid MBR template
SCRIPT_DID_MOUNT_ORIGINAL_ISO=false

# --- Sanity Checks ---
if [ "$(id -u)" -ne 0 ]; then
  echo "This script must be run as root (e.g., using sudo)." >&2
  exit 1
fi

if [ ! -f "$ORIGINAL_ISO_FILE_PATH" ]; then
    echo "Error: Original ISO file '$ORIGINAL_ISO_FILE_PATH' not found." >&2
    echo "Please set the ORIGINAL_ISO_FILE_PATH variable in the script correctly." >&2
    exit 1
fi

if [ ! -f "$KVM_DISK_IMAGE_PATH" ]; then
    echo "Error: KVM disk image '$KVM_DISK_IMAGE_PATH' not found." >&2
    exit 1
fi

echo "Checking for required tools: rsync, guestmount, mksquashfs, xorriso, mount, umount..."
for tool in rsync guestmount mksquashfs xorriso mount umount; do
    if ! command -v $tool &> /dev/null; then
        echo "Error: Required tool '$tool' not found. Please install it." >&2
        echo "On Fedora, you might need: sudo dnf install rsync libguestfs-tools squashfs-tools xorriso util-linux syslinux" >&2
        exit 1
    fi
done

if [ ! -f "$ISOHDPFX_PATH" ]; then
    echo "Error: isohybrid MBR template '$ISOHDPFX_PATH' not found." >&2
    echo "This is usually provided by the 'syslinux' package. Please install it (e.g., sudo dnf install syslinux)." >&2
    exit 1
fi

# --- Cleanup function ---
cleanup() {
    echo "Cleaning up..."
    if mountpoint -q "$KVM_DISK_MOUNT_POINT"; then
        echo "Unmounting KVM disk image from $KVM_DISK_MOUNT_POINT..."
        guestunmount "$KVM_DISK_MOUNT_POINT" || echo "Warning: Failed to unmount KVM disk $KVM_DISK_MOUNT_POINT. Manual check may be needed."
    fi

    if [ "$SCRIPT_DID_MOUNT_ORIGINAL_ISO" = true ] && mountpoint -q "$ORIGINAL_ISO_MOUNT_PATH"; then
        echo "Unmounting original ISO from $ORIGINAL_ISO_MOUNT_PATH..."
        umount "$ORIGINAL_ISO_MOUNT_PATH" || echo "Warning: Failed to unmount original ISO $ORIGINAL_ISO_MOUNT_PATH. Manual check may be needed."
    fi

    echo "Removing build root $BUILD_ROOT..."
    rm -rf "$BUILD_ROOT"
    echo "Cleanup finished."
}
trap cleanup EXIT ERR INT TERM

# --- Main Script ---
echo "Starting custom ISO creation process..."
echo "Output ISO will be: ${OUTPUT_ISO_FULL_PATH}"
echo "Build files will be in: ${BUILD_ROOT}"

# 1. Prepare working directories
echo "Preparing working directories..."
if [ -d "$BUILD_ROOT" ]; then
    echo "Warning: Previous build root found. Performing preliminary cleanup..."
    # Attempt to unmount if any mount point was somehow left mounted within BUILD_ROOT
    if mountpoint -q "$KVM_DISK_MOUNT_POINT"; then guestunmount "$KVM_DISK_MOUNT_POINT" || true; fi
    if mountpoint -q "$ORIGINAL_ISO_MOUNT_PATH"; then umount "$ORIGINAL_ISO_MOUNT_PATH" || true; fi
    rm -rf "$BUILD_ROOT"
fi
mkdir -p "$CUSTOM_ISO_WORKDIR"
mkdir -p "$KVM_DISK_MOUNT_POINT"
mkdir -p "$ORIGINAL_ISO_MOUNT_PATH"
chown -R aaron:aaron "$BUILD_ROOT"

# 1b. Mount original ISO if not already mounted
if ! mountpoint -q "$ORIGINAL_ISO_MOUNT_PATH"; then
    echo "Mounting original ISO $ORIGINAL_ISO_FILE_PATH to $ORIGINAL_ISO_MOUNT_PATH..."
    mount -o loop "$ORIGINAL_ISO_FILE_PATH" "$ORIGINAL_ISO_MOUNT_PATH"
    SCRIPT_DID_MOUNT_ORIGINAL_ISO=true
else
    echo "Original ISO mount point $ORIGINAL_ISO_MOUNT_PATH is already mounted. Using existing mount."
    # If it's already mounted, we assume the user is managing it, so the script won't try to unmount it.
    SCRIPT_DID_MOUNT_ORIGINAL_ISO=false
fi


# 2. Copy original ISO contents
echo "Copying contents from original ISO at $ORIGINAL_ISO_MOUNT_PATH to $CUSTOM_ISO_WORKDIR..."
rsync -ah --info=progress2 "$ORIGINAL_ISO_MOUNT_PATH/" "$CUSTOM_ISO_WORKDIR/"

# 3. Remove old filesystem.squashfs
echo "Removing old filesystem.squashfs from $CUSTOM_ISO_WORKDIR/casper/..."
rm -f "$CUSTOM_ISO_WORKDIR/casper/filesystem.squashfs"

# 4. Mount KVM disk image
echo "Mounting KVM disk image $KVM_DISK_IMAGE_PATH to $KVM_DISK_MOUNT_POINT (read-only)..."
guestmount -a "$KVM_DISK_IMAGE_PATH" -i --ro "$KVM_DISK_MOUNT_POINT"

# 5. Create new filesystem.squashfs
echo "Creating new filesystem.squashfs from $KVM_DISK_MOUNT_POINT..."
echo "This step can take a long time."
mksquashfs "$KVM_DISK_MOUNT_POINT" "$CUSTOM_ISO_WORKDIR/casper/filesystem.squashfs" -comp xz -Xbcj x86 -b 1M -noappend -no-progress

# 6. Unmount KVM disk image
echo "Unmounting KVM disk image from $KVM_DISK_MOUNT_POINT..."
guestunmount "$KVM_DISK_MOUNT_POINT"

# 7. Update MD5 sums
echo "Updating md5sum.txt in $CUSTOM_ISO_WORKDIR..."
current_dir=$(pwd)
cd "$CUSTOM_ISO_WORKDIR"
rm -f md5sum.txt
find . -type f -not -name md5sum.txt -print0 | xargs -0 md5sum > md5sum.txt
cd "$current_dir"

# 8. Create new ISO image
echo "Creating new ISO image: $OUTPUT_ISO_FULL_PATH..."
echo "This step can also take some time."
xorriso -as mkisofs \
  -r \
  -V "Custom_Mint_Kiosk_Live" \
  -o "$OUTPUT_ISO_FULL_PATH" \
  -J \
  -joliet-long \
  -isohybrid-mbr "$ISOHDPFX_PATH" \
  -b isolinux/isolinux.bin \
  -c isolinux/boot.cat \
  -boot-load-size 4 \
  -boot-info-table \
  -no-emul-boot \
  -eltorito-alt-boot \
  -e boot/grub/efi.img \
  -no-emul-boot \
  -isohybrid-gpt-basdat \
  "$CUSTOM_ISO_WORKDIR"

trap - EXIT ERR INT TERM # Disable trap for final explicit cleanup
cleanup

echo "-----------------------------------------------------"
echo "Custom ISO creation process completed successfully!"
echo "Your new ISO is available at: $OUTPUT_ISO_FULL_PATH"
echo "-----------------------------------------------------"

exit 0
